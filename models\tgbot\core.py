from telegram import Update
from telegram.ext import (
    Application,
    <PERSON><PERSON><PERSON><PERSON>,
    MessageHandler,
    filters,
    ContextTypes,
)

from aiolimiter import AsyncLimiter

from models.config import config
from models.logging import logger
from models.i18n_config import setup_i18n, get_text, set_locale
from models.tgbot.commands import TGBotCommandsHandler
from models.redis import RedisContextManager
from models.openrouter import OpenRouterClient


class TGBotCore:
    def __init__(self):
        self.config = config
        self.context_manager = RedisContextManager()
        self.openrouter_client = OpenRouterClient()

        self.user_rate_limiter = AsyncLimiter(config.RATE_LIMIT, 60)

        self.cmd_handler = TGBotCommandsHandler(self.context_manager, logger, config)

        self.log = logger.get_logger("TGBotCore")

        # Initialize localization with python-i18n
        setup_i18n(config.DEFAULT_LANGUAGE)
        set_locale(config.DEFAULT_LANGUAGE)

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        user = update.effective_user
        user_id = user.id
        user_message = update.message.text

        # Rate limiting
        try:
            async with self.user_rate_limiter:
                pass
        except Exception:
            await update.message.reply_text(get_text("rate_limit_exceeded"))
            return

        # Show typing action
        await context.bot.send_chat_action(
            chat_id=update.effective_chat.id,
            action="typing",
        )

        # Get context
        conversation = await self.context_manager.get_context(user_id)
        conversation.append({"role": "user", "content": user_message})

        # Generate response
        response = await self.openrouter_client.generate_response(user_id, conversation)

        if response is None:
            await update.message.reply_text(get_text("no_response"))
            return

        # Update context
        conversation.append({"role": "assistant", "content": response})
        await self.context_manager.set_context(user_id, conversation)

        # Send response (split long messages to avoid Telegram limits)
        if len(response) > 4096:
            for i in range(0, len(response), 4096):
                await update.message.reply_text(response[i : i + 4096])
        else:
            await update.message.reply_text(response)

    def setup_application(self) -> Application:
        application = Application.builder().token(config.TELEGRAM_BOT_TOKEN).build()

        # Register command handlers
        application.add_handler(CommandHandler("start", self.cmd_handler.start))
        application.add_handler(CommandHandler("help", self.cmd_handler.help_command))
        application.add_handler(CommandHandler("reset", self.cmd_handler.reset_context))
        application.add_handler(CommandHandler("model", self.cmd_handler.model_info))

        # Register message handler
        application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message)
        )

        # Error handler
        application.add_error_handler(self.error_handler)

        return application

    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE):
        self.log.error(f"Update {update} caused error: {context.error}")

        if isinstance(update, Update):
            try:
                await update.message.reply_text(get_text("unexpected_error"))
            except Exception:
                pass  # Prevent error loops

    async def shutdown(self):
        """Properly clean up resources"""
        import asyncio

        # Check if we have a running event loop
        try:
            loop = asyncio.get_running_loop()
            if loop.is_closed():
                print("Event loop is already closed, skipping async cleanup")
                return
        except RuntimeError:
            # No running loop, we're probably in a new loop created for cleanup
            pass

        shutdown_tasks = []

        # Close Redis connection
        if hasattr(self.context_manager, 'redis') and self.context_manager.redis:
            try:
                # Check if the connection is still open
                await self.context_manager.redis.ping()
                shutdown_tasks.append(self._close_redis())
            except Exception:
                # Connection is already closed or not available
                pass

        # Close OpenRouter client
        if hasattr(self.openrouter_client, 'client') and self.openrouter_client.client:
            shutdown_tasks.append(self._close_openrouter_client())

        # Close httpx session
        if hasattr(self.openrouter_client, 'session') and self.openrouter_client.session:
            shutdown_tasks.append(self._close_httpx_session())

        # Run all shutdown tasks concurrently with timeout
        if shutdown_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*shutdown_tasks, return_exceptions=True),
                    timeout=5.0  # 5 second timeout for cleanup
                )
            except asyncio.TimeoutError:
                self.log.warning("Shutdown timeout reached, forcing cleanup")

        self.log.info("Shutdown completed")

    async def _close_redis(self):
        """Close Redis connection"""
        try:
            await self.context_manager.redis.aclose()
            self.log.info("Redis connection closed successfully")
        except Exception as e:
            self.log.error(f"Redis shutdown error: {e}")

    async def _close_openrouter_client(self):
        """Close OpenRouter client"""
        try:
            await self.openrouter_client.client.close()
            self.log.info("OpenRouter client closed successfully")
        except Exception as e:
            self.log.error(f"OpenRouter client shutdown error: {e}")

    async def _close_httpx_session(self):
        """Close httpx session"""
        try:
            await self.openrouter_client.session.aclose()
            self.log.info("OpenRouter session closed successfully")
        except Exception as e:
            self.log.error(f"OpenRouter session shutdown error: {e}")
