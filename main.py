import asyncio
import logging
import signal
import sys
import threading

from models.tgbot.core import TGBot<PERSON>ore

def main():
    bot = TGBotCore()
    application = bot.setup_application()

    # Flag to track if shutdown is in progress
    shutdown_in_progress = threading.Event()

    def cleanup():
        if shutdown_in_progress.is_set():
            return
        shutdown_in_progress.set()

        print("\nShutting down gracefully...")
        try:
            # Create a new event loop for cleanup
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(bot.shutdown())
            loop.close()
        except Exception as e:
            print(f"Error during cleanup: {e}")

    # Set up signal handlers for graceful shutdown
    def signal_handler(_signum, _frame):
        cleanup()
        sys.exit(0)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    if sys.platform != "win32":
        signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Run the bot with polling
        application.run_polling()
    except KeyboardInterrupt:
        cleanup()
    except Exception as e:
        print(f"Error running bot: {e}")
        cleanup()


if __name__ == "__main__":
    # Suppress httpx INFO logs to reduce noise
    logging.getLogger("httpx").setLevel(logging.WARNING)

    main()

